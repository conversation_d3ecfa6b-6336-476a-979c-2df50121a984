'use client'

import { motion } from 'framer-motion'
import { useWishlist } from '@/contexts/WishlistContext'
import { useAuth } from '@/contexts/AuthContext'
import Button from './Button'

export default function WishlistButton({ 
  productId, 
  listId = 'default',
  size = 'md',
  variant = 'ghost',
  className = '',
  showTooltip = true,
  onAuthRequired
}) {
  const { isAuthenticated } = useAuth()
  const { isInWishlist, addToWishlist, removeFromWishlist } = useWishlist()
  
  const isWishlisted = isInWishlist(productId, listId)

  const handleClick = (e) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!isAuthenticated) {
      onAuthRequired?.()
      return
    }

    if (isWishlisted) {
      removeFromWishlist(productId, listId)
    } else {
      addToWishlist(productId, listId)
    }
  }

  const iconSize = {
    sm: 'w-6 h-6',
    md: 'w-7 h-7',
    lg: 'w-8 h-8'
  }[size]

  return (
    <div className="relative group">
      <Button
        variant={variant}
        size="icon"
        onClick={handleClick}
        className={`relative overflow-hidden ${className}`}
      >
        <motion.div
          initial={false}
          animate={{
            scale: isWishlisted ? [1, 1.2, 1] : 1,
            rotate: isWishlisted ? [0, -10, 10, 0] : 0
          }}
          transition={{ duration: 0.3 }}
        >
          {isWishlisted ? (
            <motion.svg
              className={`${iconSize} text-dark-red`}
              fill="currentColor"
              viewBox="0 0 24 24"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            >
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </motion.svg>
          ) : (
            <svg
              className={`${iconSize} text-dark-red hover:text-dark-red/80 transition-colors`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
          )}
        </motion.div>

        {/* Ripple effect */}
        {isWishlisted && (
          <motion.div
            className="absolute inset-0 bg-dark-red/20 rounded-full"
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.6 }}
          />
        )}
      </Button>

      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          {!isAuthenticated
            ? 'Inicia sesión para guardar'
            : isWishlisted
            ? 'Quitar de favoritos'
            : 'Agregar a favoritos'
          }
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
        </div>
      )}
    </div>
  )
}
