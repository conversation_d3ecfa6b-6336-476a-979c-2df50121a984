'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { ArrowLeft, Heart, Share2, ShoppingCart, Star, Truck, Shield, RotateCcw, Info, Ruler, Package, Award } from 'lucide-react'
import Image from 'next/image'
import { useWishlist } from '@/contexts/WishlistContext'
import { useCart } from '@/contexts/CartContext'
import { getProductById } from '@/lib/data/products'
import { loadRealProduct } from '@/lib/real-products-loader'

export default function ProductPage() {
  console.log('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥 PRODUCT PAGE COMPONENT EXECUTING!')
  console.log('🔥🔥🔥 RUNNING ON:', typeof window !== 'undefined' ? 'CLIENT' : 'SERVER')

  const router = useRouter()
  const params = useParams()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const { addItem } = useCart()

  console.log('🔥🔥🔥 PARAMS:', params)
  console.log('🔥🔥🔥 PARAMS.ID:', params?.id)

  // State management - OPTIMIZED FOR TRANSITIONS
  const [product, setProduct] = useState(null)
  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedModel, setSelectedModel] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('description')
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [loadingError, setLoadingError] = useState(null)

  // Enhanced mock data for demo with comprehensive product details and model variants
  const mockProduct = {
    id: params.id,
    name: "Nike Limited Edition Air Force 'Gucci'",
    brand: "Nike x Gucci",
    price: 2850,
    originalPrice: 3200,
    rating: 4.8,
    reviewCount: 127,
    images: [
      'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=800&fit=crop'
    ],
    models: [
      {
        id: 0,
        name: "Clásico Negro/Oro",
        images: [
          'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=800&h=800&fit=crop',
          'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=800&h=800&fit=crop'
        ],
        price: 2850,
        originalPrice: 3200,
        inStock: true
      },
      {
        id: 1,
        name: "Edición Blanco/Verde",
        images: [
          'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=800&h=800&fit=crop',
          'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=800&h=800&fit=crop'
        ],
        price: 2950,
        originalPrice: 3300,
        inStock: true
      },
      {
        id: 2,
        name: "Exclusivo Rojo/Oro",
        images: [
          'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=800&fit=crop',
          'https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=800&fit=crop'
        ],
        price: 3150,
        originalPrice: 3500,
        inStock: false
      }
    ],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    description: "Colaboración exclusiva entre Nike y Gucci. Diseño premium con materiales de lujo.",
    fullDescription: "Esta colaboración única entre Nike y Gucci representa la fusión perfecta entre el streetwear contemporáneo y la elegancia italiana. Cada par está meticulosamente elaborado con materiales de la más alta calidad, incluyendo cuero premium italiano y detalles en oro de 18 quilates. El diseño incorpora elementos icónicos de ambas marcas, creando una pieza verdaderamente exclusiva para coleccionistas y entusiastas de la moda.",
    features: ['Edición Limitada', 'Materiales Premium', 'Diseño Exclusivo'],
    materials: ['Cuero Italiano Premium', 'Detalles en Oro 18k', 'Suela de Goma Especializada'],
    colors: ['Negro/Oro', 'Blanco/Verde', 'Rojo/Oro'],
    type: 'sneaker',
    subType: 'low-top',
    gender: 'MIXTE',
    isLimited: true,
    isCollaboration: true,
    inStock: true,
    stockCount: 12,
    sku: 'NK-GC-AF1-2024',
    releaseDate: '2024-03-15',
    careInstructions: [
      'Limpiar con paño húmedo',
      'No sumergir en agua',
      'Usar protector de cuero',
      'Almacenar en lugar seco'
    ]
  }

  // OPTIMIZED PRODUCT LOADING - ENTERPRISE GRADE
  useEffect(() => {
    console.log('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥 MAIN USEEFFECT RUNNING!')
    console.log('🔥🔥🔥 PARAMS:', params)
    console.log('🔥🔥🔥 PARAMS.ID:', params.id)

    if (!params.id) {
      console.log('🔥🔥🔥 NO PARAMS.ID - RETURNING EARLY!')
      setLoadingError('No product ID provided')
      setIsLoading(false)
      return
    }

    console.log('🔥🔥🔥 PARAMS.ID EXISTS - CONTINUING!')

    // Start loading immediately, don't wait for isMounted
    const loadProduct = async () => {
      try {
        setIsLoading(true)
        setLoadingError(null)

        // Load real product with corrected image paths
        console.log('🔥🔥🔥 ABOUT TO CALL loadRealProduct WITH:', params.id)
        console.log('🔥🔥🔥 loadRealProduct FUNCTION TYPE:', typeof loadRealProduct)
        console.log('🔥🔥🔥 loadRealProduct FUNCTION EXISTS:', !!loadRealProduct)

        let productData = null

        // Force call the function directly
        console.log('🔥🔥🔥 CALLING loadRealProduct NOW...')
        try {
          console.log('🔥🔥🔥 BEFORE AWAIT CALL')
          productData = await loadRealProduct(params.id)
          console.log('🔥🔥🔥 AFTER AWAIT CALL')
          console.log('🔥🔥🔥 PRODUCT LOADED RESULT:', productData ? 'SUCCESS' : 'NULL')
          if (productData) {
            console.log('🔥🔥🔥 PRODUCT NAME:', productData.name)
            console.log('🔥🔥🔥 PRODUCT IMAGES COUNT:', productData.images?.length)
            console.log('🔥🔥🔥 PRODUCT MODELS COUNT:', productData.models?.length)
            console.log('🔥🔥🔥 SETTING REAL PRODUCT DATA TO STATE!')
          } else {
            console.log('🔥🔥🔥 PRODUCT DATA IS NULL OR UNDEFINED')
          }
        } catch (loadError) {
          console.error('🔥🔥🔥 ERROR IN loadRealProduct:', loadError)
          console.error('🔥🔥🔥 ERROR STACK:', loadError.stack)
          // Don't throw, just continue with fallback
        }

        // If no real product found, use mock product as fallback
        if (!productData) {
          console.log('🔥🔥🔥 USING MOCK PRODUCT AS FALLBACK')
          productData = { ...mockProduct, id: params.id }
        } else {
          console.log('🔥🔥🔥 USING REAL PRODUCT DATA!')
        }

        console.log('🔥🔥🔥 CALLING setProduct WITH:', productData ? 'REAL DATA' : 'NULL')
        setProduct(productData)
        console.log('🔥🔥🔥 setProduct CALLED SUCCESSFULLY!')
      } catch (error) {
        console.error('Error loading product:', error)
        setLoadingError(error.message)
        setProduct({ ...mockProduct, id: params.id })
      } finally {
        setIsLoading(false)
      }
    }

    loadProduct()
  }, [params.id]) // Only depend on params.id, not isMounted

  const handleAddToCart = async () => {
    if (!selectedSize) {
      alert('Por favor selecciona una talla')
      return
    }

    try {
      await addItem(product.id, selectedSize, quantity)

      // Show success toast
      showToast({
        type: 'success',
        title: '¡Agregado al carrito!',
        message: `${product.name} - Talla ${selectedSize}`,
        duration: 3000
      })
    } catch (error) {
      console.error('Error adding item to cart:', error)
      showToast({
        type: 'error',
        title: 'Error',
        message: 'No se pudo agregar el producto al carrito',
        duration: 3000
      })
    }
  }

  // Toast notification function
  const showToast = ({ type, title, message, duration = 3000 }) => {
    const toast = document.createElement('div')
    toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0`

    const bgColor = type === 'success' ? 'bg-lime-500' : 'bg-red-500'
    const icon = type === 'success' ? '✓' : '✕'

    toast.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 ${bgColor} rounded-full flex items-center justify-center text-black font-bold">
              ${icon}
            </div>
          </div>
          <div class="ml-3 flex-1">
            <p class="text-sm font-semibold text-gray-900 dark:text-white">${title}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">${message}</p>
          </div>
          <button class="ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.parentElement.parentElement.parentElement.remove()">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    `

    document.body.appendChild(toast)

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full', 'opacity-0')
      toast.classList.add('translate-x-0', 'opacity-100')
    }, 100)

    // Auto remove
    setTimeout(() => {
      toast.classList.add('translate-x-full', 'opacity-0')
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast)
        }
      }, 300)
    }, duration)
  }

  const handleWishlistToggle = () => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product)
    }
  }

  // OPTIMIZED LOADING STATE - ENTERPRISE GRADE
  if (isLoading || !product) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 pt-16 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500 mx-auto"></div>
          <p className="text-gray-600 dark:text-gray-400">Cargando producto...</p>
          {loadingError && (
            <p className="text-red-500 text-sm">Error: {loadingError}</p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Back Button */}
        <button
          onClick={() => router.back()}
          className="flex items-center gap-2 text-gray-600 hover:text-lime-600 mb-8 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          Volver
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">

          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="aspect-square bg-gray-100 rounded-2xl overflow-hidden">
              {product.images && product.images[selectedImage] ? (
                <Image
                  src={product.images[selectedImage]}
                  alt={product.name}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <Package className="w-24 h-24" />
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-2">
              {product.images?.slice(0, 4).map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImage === index
                      ? 'border-lime-500'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    width={150}
                    height={150}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">

            {/* Badges */}
            <div className="flex flex-wrap gap-2">
              {product.isLimited && (
                <span className="px-3 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full">
                  Edición Limitada
                </span>
              )}
              {product.isNew && (
                <span className="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                  Nuevo
                </span>
              )}
              {product.isCollaboration && (
                <span className="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">
                  Colaboración
                </span>
              )}
            </div>

            {/* Brand & Name */}
            <div>
              <p className="text-gray-600 font-medium mb-2">{product.brand}</p>
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {product.name}
              </h1>
              <p className="text-gray-600 leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-gray-600">
                {product.rating} ({product.reviewCount} reseñas)
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-4">
              <span className="text-3xl font-bold text-lime-600">
                ${product.price.toLocaleString()} MXN
              </span>
              {product.originalPrice && (
                <span className="text-xl text-gray-500 line-through">
                  ${product.originalPrice.toLocaleString()} MXN
                </span>
              )}
              {product.originalPrice && (
                <span className="px-2 py-1 bg-red-100 text-red-800 text-sm font-medium rounded">
                  -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                </span>
              )}
            </div>

            {/* Size Selection */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Talla
              </h3>
              <div className="grid grid-cols-5 gap-2">
                {product.sizes?.map((size) => (
                  <button
                    key={size}
                    onClick={() => setSelectedSize(size)}
                    className={`py-3 px-4 rounded-lg border transition-all ${
                      selectedSize === size
                        ? 'bg-lime-500 text-black border-lime-500'
                        : 'border-gray-300 text-gray-700 hover:border-lime-500 hover:text-lime-600'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Cantidad
              </h3>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                  className="w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:border-lime-500 disabled:opacity-50"
                >
                  -
                </button>

                <span className="w-12 text-center font-semibold text-gray-900 dark:text-white">
                  {quantity}
                </span>

                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:border-lime-500"
                >
                  +
                </button>
              </div>
            </div>

            {/* Add to Cart */}
            <div className="space-y-3">
              <button
                onClick={handleAddToCart}
                disabled={!selectedSize}
                className="w-full bg-lime-500 hover:bg-lime-600 disabled:bg-gray-300 text-black font-medium py-4 px-6 rounded-lg transition-colors"
              >
                {selectedSize
                  ? `Agregar al Carrito - $${(product.price * quantity).toLocaleString()} MXN`
                  : 'Selecciona una talla'
                }
              </button>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handleWishlistToggle}
                  className="flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-lg hover:border-lime-500 transition-colors"
                >
                  <Heart className={`w-5 h-5 ${isInWishlist(product.id) ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
                  Favoritos
                </button>

                <button
                  onClick={() => setShowShareModal(true)}
                  className="flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-lg hover:border-lime-500 transition-colors"
                >
                  <Share2 className="w-5 h-5 text-gray-600" />
                  Compartir
                </button>
              </div>
            </div>

            {/* Product Features */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Características
              </h3>
              <ul className="space-y-2">
                {product.features?.map((feature, index) => (
                  <li
                    key={index}
                    className="flex items-center gap-2 text-gray-600"
                  >
                    <div className="w-2 h-2 bg-lime-500 rounded-full" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Product Info */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">SKU:</span>
                <span className="text-gray-900 ml-2">
                  {product.sku}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Stock:</span>
                <span className="text-gray-900 ml-2">
                  {product.stockCount} disponibles
                </span>
              </div>
              <div>
                <span className="text-gray-500">Género:</span>
                <span className="text-gray-900 ml-2">
                  {product.gender}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Lanzamiento:</span>
                <span className="text-gray-900 ml-2">
                  {new Date(product.releaseDate).toLocaleDateString('es-MX')}
                </span>
              </div>
            </div>

            {/* Product Description */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Descripción
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {product.fullDescription}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Compartir Producto</h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            <div className="space-y-3">
              <button className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                  f
                </div>
                Compartir en Facebook
              </button>
              <button className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm">
                  IG
                </div>
                Compartir en Instagram
              </button>
              <button className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">
                  W
                </div>
                Compartir en WhatsApp
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
