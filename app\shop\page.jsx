'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { products as mockProducts } from '@/data/products'
import { getAllRealProducts } from '@/lib/real-products-loader'
import AnimatedProductCard from '@/components/ui/AnimatedProductCard'

export default function ShopPage() {
  console.log('🛍️🛍️🛍️ OPTIMIZED SHOP PAGE COMPONENT RENDERING!')

  const [products, setProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const productsPerPage = 15 // FIXED: 15 products = 3 rows minimum (5 cols max = 15 products)
  const topRef = useRef(null)

  // Load products on mount
  useEffect(() => {
    console.log('🛍️ SHOP DEBUG: useEffect is running!')
    const loadProducts = async () => {
      setIsLoading(true)
      try {
        console.log('🛍️🛍️🛍️ NEW SHOP PAGE: Loading products...')
        console.log('🛍️🛍️🛍️ NEW SHOP PAGE: getAllRealProducts function type:', typeof getAllRealProducts)
        console.log('🛍️🛍️🛍️ NEW SHOP PAGE: mockProducts length:', mockProducts.length)

        // Try to get real products first
        const realProducts = getAllRealProducts()
        console.log('🛍️🛍️🛍️ NEW SHOP PAGE: Real products loaded:', realProducts.length)

        if (realProducts.length > 0) {
          console.log('🛍️🛍️🛍️ NEW SHOP PAGE: ✅ Using REAL products')
          setProducts(realProducts)
        } else {
          console.log('🛍️🛍️🛍️ NEW SHOP PAGE: ⚠️ Using mock products fallback')
          setProducts(mockProducts)
        }
      } catch (error) {
        console.error('🛍️🛍️🛍️ NEW SHOP PAGE: Error loading products:', error)
        setProducts(mockProducts)
      } finally {
        setIsLoading(false)
        console.log('🛍️🛍️🛍️ NEW SHOP PAGE: ✅ Loading complete')
      }
    }

    loadProducts()
  }, [])

  // Pagination with scroll-to-top functionality
  const indexOfLastProduct = currentPage * productsPerPage
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage
  const currentProducts = products.slice(indexOfFirstProduct, indexOfLastProduct)
  const totalPages = Math.ceil(products.length / productsPerPage)

  // Debug: Log current products being rendered
  console.log('🛍️ SHOP DEBUG: Total products:', products.length)
  console.log('🛍️ SHOP DEBUG: Current products:', currentProducts.length)
  console.log('🛍️ SHOP DEBUG: First product:', currentProducts[0])

  // Scroll to top when page changes
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage)
    // Smooth scroll to top of products section
    if (topRef.current) {
      topRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-neutral-900">
      {/* Header */}
      <div className="bg-pure-white dark:bg-neutral-900 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-godber font-bold text-pure-black dark:text-pure-white mb-4 tracking-wider">
              TIENDA
            </h1>
            <p className="text-lg font-poppins text-text-gray dark:text-text-gray max-w-2xl mx-auto">
              Descubre nuestra colección completa de calzado de lujo y streetwear exclusivo.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Products Count - Scroll target */}
        <div ref={topRef} className="mb-8">
          <p className="text-text-gray font-poppins">
            {isLoading ? 'Cargando productos...' : `${products.length} productos encontrados`}
          </p>
        </div>

        {/* FIXED: Loading State - Same grid as products */}
        {isLoading ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-5 gap-4 sm:gap-6"
          >
            {[...Array(15)].map((_, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.05,
                  ease: "easeOut"
                }}
                className="h-96 bg-gradient-to-br from-white/5 to-white/10 rounded-2xl border border-white/10 overflow-hidden relative"
              >
                {/* Shimmer Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
                
                {/* Image Skeleton */}
                <div className="h-64 bg-gradient-to-br from-lime-400/10 to-lime-600/5 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-lime-400/20 to-transparent animate-pulse"></div>
                </div>
                
                {/* Content Skeleton */}
                <div className="p-4 space-y-3">
                  <div className="h-4 bg-white/10 rounded-lg animate-pulse"></div>
                  <div className="h-3 bg-white/5 rounded-lg w-3/4 animate-pulse"></div>
                  <div className="flex items-center justify-between">
                    <div className="h-6 bg-lime-400/20 rounded-lg w-20 animate-pulse"></div>
                    <div className="w-8 h-8 bg-lime-400/20 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          /* FIXED: Responsive Products Grid - XS:2, SM:2, MD:3, LG:4, XL:5, 2XL:5 (max) */
          <motion.div
            className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-5 gap-4 sm:gap-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="popLayout">
              {currentProducts.map((product, index) => (
                <AnimatedProductCard
                  key={`${product.id}-${currentPage}`}
                  product={product}
                  index={index}
                  onAuthRequired={() => console.log('Auth required')}
                />
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* Optimized Pagination with Scroll-to-Top */}
        {!isLoading && totalPages > 1 && (
          <motion.div
            className="mt-12 flex justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="flex items-center space-x-4">
              <motion.button
                onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
                className="px-6 py-3 bg-pure-white dark:bg-gray-800 text-pure-black dark:text-pure-white border border-gray-300 dark:border-gray-600 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-lime-green hover:text-pure-black hover:border-lime-green transition-all duration-300 font-poppins font-medium"
                whileHover={{ scale: currentPage === 1 ? 1 : 1.05 }}
                whileTap={{ scale: currentPage === 1 ? 1 : 0.95 }}
              >
                ← Anterior
              </motion.button>

              <div className="flex items-center space-x-2">
                {[...Array(totalPages)].map((_, index) => {
                  const pageNum = index + 1
                  const isCurrentPage = pageNum === currentPage

                  // Show first page, last page, current page, and pages around current
                  const shouldShow = pageNum === 1 ||
                                   pageNum === totalPages ||
                                   Math.abs(pageNum - currentPage) <= 1

                  if (!shouldShow) {
                    // Show ellipsis for gaps
                    if (pageNum === currentPage - 2 || pageNum === currentPage + 2) {
                      return <span key={pageNum} className="text-gray-400">...</span>
                    }
                    return null
                  }

                  return (
                    <motion.button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`w-10 h-10 rounded-lg font-poppins font-medium transition-all duration-300 ${
                        isCurrentPage
                          ? 'bg-lime-green text-pure-black shadow-lg'
                          : 'bg-pure-white dark:bg-gray-800 text-pure-black dark:text-pure-white border border-gray-300 dark:border-gray-600 hover:bg-lime-green hover:text-pure-black hover:border-lime-green'
                      }`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {pageNum}
                    </motion.button>
                  )
                })}
              </div>

              <motion.button
                onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-6 py-3 bg-pure-white dark:bg-gray-800 text-pure-black dark:text-pure-white border border-gray-300 dark:border-gray-600 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-lime-green hover:text-pure-black hover:border-lime-green transition-all duration-300 font-poppins font-medium"
                whileHover={{ scale: currentPage === totalPages ? 1 : 1.05 }}
                whileTap={{ scale: currentPage === totalPages ? 1 : 0.95 }}
              >
                Siguiente →
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* No Products */}
        {!isLoading && products.length === 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">👟</div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
              No se encontraron productos
            </h3>
            <p className="text-warm-camel mb-4">
              Intenta recargar la página
            </p>
          </motion.div>
        )}

        {/* Development Info */}
        {process.env.NODE_ENV === 'development' && !isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-8 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl"
          >
            <div className="text-xs text-blue-400 space-y-1">
              <div>📊 <strong>Shop Page Status:</strong></div>
              <div>• Products loaded: {products.length}</div>
              <div>• Current page: {currentPage}/{totalPages}</div>
              <div>• Products per page: {productsPerPage}</div>
              <div>• Grid: grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6</div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
