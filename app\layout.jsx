import { Inter, Playfair_Display, Fira_Code } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '../components/theme/ThemeProvider'
import { AuthProvider } from '../contexts/AuthContext'
import { CartProvider } from '../contexts/CartContext'
import { WishlistProvider } from '../contexts/WishlistContext'
import { RouteTransitionProvider, PageTransitionWrapper } from '../components/transitions/RouteTransitionProvider'
import NavigationProgress from '../components/transitions/NavigationProgress'
import { UserPreferencesProvider } from '../contexts/UserPreferencesContext'
import Header from '../components/layout/Header'
import Footer from '../components/layout/Footer'
import FloatingActionMenu from '../components/features/FloatingActionMenu'
import MobileLayout from '../components/layout/MobileLayout'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
})

const firaCode = Fira_Code({
  subsets: ['latin'],
  variable: '--font-fira',
  display: 'swap',
})

export const metadata = {
  title: {
    default: 'The White Laces | Luxury Streetwear Footwear',
    template: '%s | The White Laces'
  },
  description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands. Mexico-first, LATAM-ready.',
  keywords: ['luxury sneakers', 'streetwear', 'limited edition', 'premium footwear', 'Mexico', 'LATAM'],
  authors: [{ name: 'The White Laces Team' }],
  creator: 'The White Laces',
  publisher: 'The White Laces',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://thewhitelaces.com'),
  alternates: {
    canonical: '/',
    languages: {
      'es-MX': '/es-MX',
      'en-US': '/en-US',
      'pt-BR': '/pt-BR',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_MX',
    url: 'https://thewhitelaces.com',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    siteName: 'The White Laces',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'The White Laces - Luxury Streetwear Footwear',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    images: ['/og-image.jpg'],
    creator: '@thewhitelaces',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="es-MX" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${playfair.variable} ${firaCode.variable} font-inter antialiased`}
        suppressHydrationWarning
      >
        <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine text-forest-emerald dark:text-light-cloud-gray transition-colors duration-300">
          {/* Simple Header */}
          <header className="fixed top-0 left-0 right-0 z-50 glass backdrop-blur-xl border-b border-white/10">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between h-16 lg:h-20">
                {/* Logo */}
                <div className="flex-shrink-0">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-forest-emerald font-bold text-lg">W</span>
                    </div>
                    <span className="text-xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray">
                      The White Laces
                    </span>
                  </div>
                </div>

                {/* Navigation */}
                <nav className="hidden md:flex space-x-8">
                  <a href="/shop" className="text-forest-emerald dark:text-light-cloud-gray hover:text-primary transition-colors">
                    Tienda
                  </a>
                  <a href="/ai-features" className="text-forest-emerald dark:text-light-cloud-gray hover:text-primary transition-colors">
                    IA
                  </a>
                  <a href="/community" className="text-forest-emerald dark:text-light-cloud-gray hover:text-primary transition-colors">
                    Social
                  </a>
                </nav>

                {/* Actions */}
                <div className="flex items-center space-x-4">
                  <button className="p-2 text-forest-emerald dark:text-light-cloud-gray hover:text-primary transition-colors">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                  <button className="p-2 text-forest-emerald dark:text-light-cloud-gray hover:text-primary transition-colors">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </button>
                  <button className="p-2 text-forest-emerald dark:text-light-cloud-gray hover:text-primary transition-colors">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </header>

          <main className="pt-16 lg:pt-20">
            {children}
          </main>
        </div>
      </body>
    </html>
  )
}
