'use client'

import { createContext, useContext, useReducer, useEffect } from 'react'
import { useAuth } from './AuthContext'
import { getProductById } from '@/lib/data/products'

// Wishlist Context
const WishlistContext = createContext()

// Wishlist Actions
const WISHLIST_ACTIONS = {
  ADD_ITEM: 'ADD_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  CLEAR_WISHLIST: 'CLEAR_WISHLIST',
  LOAD_WISHLIST: 'LOAD_WISHLIST',
  CREATE_LIST: 'CREATE_LIST',
  DELETE_LIST: 'DELETE_LIST',
  UPDATE_LIST: 'UPDATE_LIST',
  MOVE_ITEM: 'MOVE_ITEM'
}

// Wishlist Reducer
const wishlistReducer = (state, action) => {
  switch (action.type) {
    case WISHLIST_ACTIONS.ADD_ITEM: {
      const { listId, productId } = action.payload
      const updatedLists = state.lists.map(list => {
        if (list.id === listId) {
          // Check if item already exists
          if (list.items.includes(productId)) {
            return list
          }
          return {
            ...list,
            items: [...list.items, productId],
            updatedAt: new Date().toISOString()
          }
        }
        return list
      })
      
      return {
        ...state,
        lists: updatedLists,
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.REMOVE_ITEM: {
      const { listId, productId } = action.payload
      const updatedLists = state.lists.map(list => {
        if (list.id === listId) {
          return {
            ...list,
            items: list.items.filter(id => id !== productId),
            updatedAt: new Date().toISOString()
          }
        }
        return list
      })
      
      return {
        ...state,
        lists: updatedLists,
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.CREATE_LIST: {
      const newList = {
        id: Date.now().toString(),
        name: action.payload.name,
        description: action.payload.description || '',
        isPrivate: action.payload.isPrivate || false,
        items: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      return {
        ...state,
        lists: [...state.lists, newList],
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.DELETE_LIST: {
      return {
        ...state,
        lists: state.lists.filter(list => list.id !== action.payload.listId),
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.UPDATE_LIST: {
      const { listId, updates } = action.payload
      const updatedLists = state.lists.map(list => {
        if (list.id === listId) {
          return {
            ...list,
            ...updates,
            updatedAt: new Date().toISOString()
          }
        }
        return list
      })
      
      return {
        ...state,
        lists: updatedLists,
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.MOVE_ITEM: {
      const { fromListId, toListId, productId } = action.payload
      let updatedLists = state.lists.map(list => {
        if (list.id === fromListId) {
          return {
            ...list,
            items: list.items.filter(id => id !== productId),
            updatedAt: new Date().toISOString()
          }
        }
        return list
      })
      
      updatedLists = updatedLists.map(list => {
        if (list.id === toListId && !list.items.includes(productId)) {
          return {
            ...list,
            items: [...list.items, productId],
            updatedAt: new Date().toISOString()
          }
        }
        return list
      })
      
      return {
        ...state,
        lists: updatedLists,
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.CLEAR_WISHLIST: {
      return {
        ...initialWishlistState,
        updatedAt: new Date().toISOString()
      }
    }
    
    case WISHLIST_ACTIONS.LOAD_WISHLIST: {
      return {
        ...state,
        ...action.payload
      }
    }
    
    default:
      return state
  }
}

// Initial Wishlist State
const initialWishlistState = {
  lists: [
    {
      id: 'default',
      name: 'Mi Lista de Deseos',
      description: 'Mis productos favoritos',
      isPrivate: false,
      items: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

// Wishlist Provider Component
export function WishlistProvider({ children }) {
  const { user, isAuthenticated } = useAuth()
  const [state, dispatch] = useReducer(wishlistReducer, initialWishlistState)
  
  // Load wishlist from localStorage on mount
  useEffect(() => {
    // TEMPORARY: Allow wishlist without authentication for testing
    const userId = user?.id || 'guest'
    const savedWishlist = localStorage.getItem(`twl-wishlist-${userId}`)
    if (savedWishlist) {
      try {
        const parsedWishlist = JSON.parse(savedWishlist)
        dispatch({
          type: WISHLIST_ACTIONS.LOAD_WISHLIST,
          payload: parsedWishlist
        })
      } catch (error) {
        console.error('Error loading wishlist from localStorage:', error)
      }
    }
  }, [isAuthenticated, user])
  
  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    // TEMPORARY: Allow wishlist without authentication for testing
    const userId = user?.id || 'guest'
    localStorage.setItem(`twl-wishlist-${userId}`, JSON.stringify(state))
  }, [state, isAuthenticated, user])
  
  // Clear wishlist when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      dispatch({ type: WISHLIST_ACTIONS.CLEAR_WISHLIST })
    }
  }, [isAuthenticated])
  
  // Wishlist Actions
  const addToWishlist = (productId, listId = 'default') => {
    console.log('💚 WishlistContext: addToWishlist called', { productId, listId, isAuthenticated, user })
    dispatch({
      type: WISHLIST_ACTIONS.ADD_ITEM,
      payload: { listId, productId }
    })
  }

  const removeFromWishlist = (productId, listId = 'default') => {
    console.log('💔 WishlistContext: removeFromWishlist called', { productId, listId, isAuthenticated, user })
    dispatch({
      type: WISHLIST_ACTIONS.REMOVE_ITEM,
      payload: { listId, productId }
    })
  }
  
  const createWishlist = (name, description = '', isPrivate = false) => {
    dispatch({
      type: WISHLIST_ACTIONS.CREATE_LIST,
      payload: { name, description, isPrivate }
    })
  }
  
  const deleteWishlist = (listId) => {
    if (listId === 'default') return // Can't delete default list
    dispatch({
      type: WISHLIST_ACTIONS.DELETE_LIST,
      payload: { listId }
    })
  }
  
  const updateWishlist = (listId, updates) => {
    dispatch({
      type: WISHLIST_ACTIONS.UPDATE_LIST,
      payload: { listId, updates }
    })
  }
  
  const moveItem = (productId, fromListId, toListId) => {
    dispatch({
      type: WISHLIST_ACTIONS.MOVE_ITEM,
      payload: { productId, fromListId, toListId }
    })
  }
  
  const clearWishlist = () => {
    dispatch({ type: WISHLIST_ACTIONS.CLEAR_WISHLIST })
  }
  
  // Utility functions
  const isInWishlist = (productId, listId = 'default') => {
    const list = state.lists.find(l => l.id === listId)
    return list ? list.items.includes(productId) : false
  }
  
  const getWishlistItems = (listId = 'default') => {
    const list = state.lists.find(l => l.id === listId)
    if (!list) return []
    
    return list.items.map(productId => {
      const product = getProductById(productId)
      return product ? { productId, product } : null
    }).filter(Boolean)
  }
  
  const getAllWishlistItems = () => {
    const allItems = []
    state.lists.forEach(list => {
      list.items.forEach(productId => {
        const product = getProductById(productId)
        if (product && !allItems.find(item => item.productId === productId)) {
          allItems.push({ productId, product, listId: list.id, listName: list.name })
        }
      })
    })
    return allItems
  }
  
  const getTotalItemsCount = () => {
    return state.lists.reduce((total, list) => total + list.items.length, 0)
  }
  
  const getWishlistSummary = () => {
    return {
      totalLists: state.lists.length,
      totalItems: getTotalItemsCount(),
      lists: state.lists.map(list => ({
        id: list.id,
        name: list.name,
        itemCount: list.items.length,
        isPrivate: list.isPrivate
      }))
    }
  }
  
  const value = {
    // State
    wishlists: state.lists,
    summary: getWishlistSummary(),
    
    // Actions
    addToWishlist,
    removeFromWishlist,
    createWishlist,
    deleteWishlist,
    updateWishlist,
    moveItem,
    clearWishlist,
    
    // Utilities
    isInWishlist,
    getWishlistItems,
    getAllWishlistItems,
    getTotalItemsCount
  }
  
  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  )
}

// Custom hook to use wishlist context
export function useWishlist() {
  const context = useContext(WishlistContext)
  
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider')
  }
  
  return context
}
