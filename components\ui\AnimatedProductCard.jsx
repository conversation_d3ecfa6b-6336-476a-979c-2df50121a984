'use client'

import { useState, useCallback } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import WishlistButton from '@/components/ui/WishlistButton'
import { ShoppingCart } from 'lucide-react'
import { formatPrice } from '@/lib/utils'

export default function AnimatedProductCard({ product, index = 0, onAuthRequired }) {
  const { addItem } = useCart()
  const { isAuthenticated } = useAuth()
  const [isHovered, setIsHovered] = useState(false)
  const [selectedSize, setSelectedSize] = useState('')
  const [showSizeSelector, setShowSizeSelector] = useState(false)
  const [imageError, setImageError] = useState(false)

  // Optimized hover handlers with useCallback for performance
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  // Get primary and secondary images from real products
  const primaryImage = product?.images?.[0] || product?.image || '/images/placeholder-shoe.jpg'
  const secondaryImage = product?.images?.[1] || product?.image2 || primaryImage

  const handleAddToCart = useCallback(() => {
    console.log('🛒 Add to cart clicked for product:', product?.id, 'size:', selectedSize)

    if (!selectedSize) {
      setShowSizeSelector(true)
      return
    }

    // Add item to cart
    addItem(product.id, selectedSize, 1)
    setShowSizeSelector(false)
    setSelectedSize('')

    console.log('✅ Item added to cart successfully')
  }, [selectedSize, addItem, product.id])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.4,
        delay: index * 0.05,
        ease: "easeOut"
      }}
      className="cursor-pointer h-full w-full group"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <motion.div
        className="h-full"
        whileHover={{ 
          scale: 1.02,
          y: -4
        }}
        transition={{ 
          duration: 0.3,
          ease: "easeOut"
        }}
      >
        <Card
          variant="default"
          className={`product-card-container relative overflow-hidden transition-all duration-300 h-full bg-white dark:bg-neutral-800 border ${
            isHovered ? 'shadow-xl border-lime-green/30' : 'shadow-md border-gray-200/50 dark:border-gray-700/50'
          }`}
        >
          <CardContent className="p-0 h-full flex flex-col">
            {/* Product Image Container - Fixed aspect ratio */}
            <div className="product-card-image relative bg-gray-50 dark:bg-gray-800 overflow-hidden">
              {/* Optimized Image Display for Real Products */}
              {!imageError ? (
                <div className="relative w-full h-full">
                  {/* Primary Image */}
                  <img
                    src={primaryImage}
                    alt={product?.name || 'Product Image'}
                    className={`w-full h-full object-cover transition-all duration-300 ease-out ${
                      isHovered && secondaryImage !== primaryImage ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
                    }`}
                    onError={() => setImageError(true)}
                    loading={index < 8 ? "eager" : "lazy"}
                  />

                  {/* Secondary Image (Hover Effect) */}
                  {secondaryImage && secondaryImage !== primaryImage && (
                    <img
                      src={secondaryImage}
                      alt={`${product?.name} - Vista alternativa`}
                      className={`absolute inset-0 w-full h-full object-cover transition-all duration-300 ease-out ${
                        isHovered ? 'opacity-100 scale-105' : 'opacity-0 scale-100'
                      }`}
                      onError={() => setImageError(true)}
                      loading="lazy"
                    />
                  )}

                  {/* Subtle overlay for better contrast */}
                  <div className={`absolute inset-0 bg-black/5 transition-opacity duration-300 ${
                    isHovered ? 'opacity-100' : 'opacity-0'
                  }`} />
                </div>
              ) : (
                /* Fallback Placeholder */
                <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                  <div className="text-center p-4">
                    <div className="text-4xl mb-2">👟</div>
                    <span className="text-gray-600 dark:text-gray-400 text-sm font-medium font-poppins">
                      {product?.name || 'Product Image'}
                    </span>
                  </div>
                </div>
              )}

              {/* Badges */}
              <div className="absolute top-3 left-3 flex flex-col gap-2 z-10">
                {product?.isLimited && (
                  <Badge variant="limited" size="sm" pulse>
                    Limitado
                  </Badge>
                )}
                {product?.isVip && (
                  <Badge variant="vip" size="sm">
                    VIP
                  </Badge>
                )}
                {product?.isNew && (
                  <Badge variant="success" size="sm">
                    Nuevo
                  </Badge>
                )}
              </div>

              {/* Quick Actions */}
              <div className={`absolute top-3 right-3 flex flex-col gap-2 z-10 transition-all duration-300 ${
                isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
              }`}>
                <WishlistButton
                  productId={product?.id}
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 bg-white/80 hover:bg-white/90 backdrop-blur-sm shadow-sm border border-gray-200/50"
                  onAuthRequired={onAuthRequired}
                />
              </div>
            </div>

            {/* Product Info - Optimized layout */}
            <div className="product-card-content space-y-3 relative p-3">
              {/* Brand & Name */}
              <Link href={`/product/${product?.id}`} className="block">
                <div className="product-card-title cursor-pointer">
                  <p className={`text-xs font-medium font-poppins uppercase tracking-wide mb-1 transition-opacity duration-300 ${
                    isHovered ? 'text-gray-600 opacity-100' : 'text-gray-500 opacity-80'
                  }`}>
                    {product?.brand || 'Premium Brand'}
                  </p>
                  <h3 className={`font-semibold line-clamp-2 transition-colors duration-300 font-poppins text-sm leading-tight ${
                    isHovered 
                      ? 'text-lime-green dark:text-lime-green' 
                      : 'text-gray-900 dark:text-gray-100'
                  }`}>
                    {product?.name || 'Luxury Sneaker Collection'}
                  </h3>
                </div>
              </Link>

              {/* Price - Enhanced styling */}
              <div className={`product-card-price flex items-center gap-2 transition-transform duration-300 ${
                isHovered ? 'scale-105' : 'scale-100'
              }`}>
                <span className="text-xl font-bold text-lime-green-dark font-poppins">
                  ${product?.price || 210}
                </span>
                {product?.originalPrice && (
                  <span className="text-sm text-gray-500 line-through font-poppins">
                    ${product.originalPrice}
                  </span>
                )}
              </div>

              {/* Size Selector */}
              {showSizeSelector && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-2"
                >
                  <p className="text-xs text-gray-600">Selecciona tu talla:</p>
                  <div className="grid grid-cols-4 gap-1">
                    {(product?.sizes || ['36', '37', '38', '39', '40', '41', '42', '43']).slice(0, 8).map((size) => (
                      <button
                        key={size}
                        onClick={() => setSelectedSize(size)}
                        className={`text-xs py-1 px-2 rounded border transition-colors font-poppins ${
                          selectedSize === size
                            ? 'bg-lime-green text-black border-lime-green'
                            : 'border-gray-300 text-gray-600 hover:border-lime-green hover:text-lime-green'
                        }`}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Round Lime Green Cart Button */}
              <div className="mt-auto pt-2">
                <motion.button
                  onClick={handleAddToCart}
                  className="w-10 h-10 bg-lime-green rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 ml-auto hover:bg-lime-green/90"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  <ShoppingCart className="w-4 h-4 text-black" />
                </motion.button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}
