'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useWishlist } from '@/contexts/WishlistContext'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { getAllRealProducts } from '@/lib/real-products-loader'
import { Heart, ShoppingCart, Trash2, Share2 } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Link from 'next/link'
import Image from 'next/image'

export default function WishlistPage() {
  const { wishlist, addToWishlist, removeFromWishlist, clearWishlist } = useWishlist()
  const { isAuthenticated, user } = useAuth()
  const { addItem } = useCart()
  const [selectedList, setSelectedList] = useState('default')
  const [allProducts, setAllProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  // Get current wishlist items with proper error handling
  const currentList = (wishlist && wishlist[selectedList]) ? wishlist[selectedList] : []
  const totalItems = wishlist ? Object.values(wishlist).reduce((total, list) => total + list.length, 0) : 0

  // Load all products on mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const realProducts = getAllRealProducts()
        setAllProducts(realProducts)
      } catch (error) {
        console.error('Error loading products for wishlist:', error)
      } finally {
        setIsLoading(false)
      }
    }
    loadProducts()
  }, [])

  // Get real product details from loaded products
  const getProductDetails = (productId) => {
    const product = allProducts.find(p => p.id === productId || p.id === parseInt(productId))

    if (product) {
      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.images?.[0] || '/products/placeholder.webp',
        category: product.category,
        inStock: product.inStock !== false,
        sizes: product.sizes || [],
        colors: product.colors || []
      }
    }

    // Fallback for unknown products
    return {
      id: productId,
      name: `Producto ${productId}`,
      brand: 'TWL',
      price: 2500,
      originalPrice: 3200,
      image: '/products/placeholder.webp',
      category: 'Sneakers',
      inStock: true,
      sizes: ['36', '37', '38', '39', '40', '41', '42'],
      colors: ['Disponible']
    }
  }

  const handleRemoveItem = (productId) => {
    removeFromWishlist(productId, selectedList)
  }

  const handleClearWishlist = () => {
    clearWishlist(selectedList)
  }

  const handleAddToCart = (product) => {
    // Add to cart with default size and quantity
    const defaultSize = product.sizes?.[0] || '40'
    addItem({
      id: product.id,
      name: product.name,
      brand: product.brand,
      price: product.price,
      image: product.image,
      size: defaultSize,
      quantity: 1
    })
    console.log('Added to cart:', product.name)
  }

  const handleShare = (product) => {
    // Share functionality
    console.log('Sharing product:', product)
  }

  // Test function to manually add a product to wishlist
  const handleTestAddToWishlist = () => {
    console.log('🧪 Test: Adding product to wishlist manually')
    alert('🧪 Test button clicked! Check console for details.')

    try {
      console.log('🧪 Test: addToWishlist function type:', typeof addToWishlist)
      console.log('🧪 Test: Current wishlist state:', wishlist)

      addToWishlist('test-product-123', 'default')

      console.log('🧪 Test: Product added successfully')
      alert('✅ Product added to wishlist successfully!')
    } catch (error) {
      console.error('❌ Test: Error adding to wishlist:', error)
      alert('❌ Error: ' + error.message)
    }
  }

  // Show loading state while contexts are initializing
  if (isLoading) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-fog-black">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-lime-green-dark mx-auto mb-4"></div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-pure-white mb-2">
              Cargando Lista de Deseos...
            </h1>
          </div>
        </div>
      </div>
    )
  }

  if (!isAuthenticated && currentList.length === 0) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-fog-black">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-pure-white mb-2">
              Tu Lista de Deseos está Vacía
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Descubre productos increíbles y guárdalos para más tarde
            </p>
            <Link href="/shop">
              <Button className="bg-lime-green-dark hover:bg-lime-green text-pure-black font-medium">
                Explorar Productos
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-fog-black">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-pure-white mb-2">
              Mi Lista de Deseos
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {totalItems} {totalItems === 1 ? 'producto guardado' : 'productos guardados'}
            </p>
            {/* Test Buttons - MULTIPLE TESTS */}
            <div className="mt-4 p-4 bg-blue-100 border-2 border-blue-500 rounded-lg">
              <p className="text-blue-800 text-sm mb-3">🧪 Testing Mode - Click Event Debugging:</p>

              {/* Test 1: Basic HTML Button */}
              <div className="mb-3">
                <p className="text-xs text-blue-700 mb-1">Test 1: Basic HTML Button</p>
                <button
                  onClick={handleTestAddToWishlist}
                  className="bg-red-500 hover:bg-red-600 text-white font-bold px-4 py-2 rounded"
                  style={{ zIndex: 9999 }}
                >
                  🔴 HTML Button Test
                </button>
              </div>

              {/* Test 2: Button Component */}
              <div className="mb-3">
                <p className="text-xs text-blue-700 mb-1">Test 2: Button Component</p>
                <Button
                  onClick={handleTestAddToWishlist}
                  className="bg-blue-500 hover:bg-blue-600 text-white font-bold px-4 py-2"
                >
                  🔵 Button Component Test
                </Button>
              </div>

              {/* Test 3: Simple Click Test */}
              <div className="mb-3">
                <p className="text-xs text-blue-700 mb-1">Test 3: Simple Alert Test</p>
                <button
                  onClick={() => {
                    console.log('🟢 Simple click test!')
                    alert('🟢 Simple click works!')
                  }}
                  className="bg-green-500 hover:bg-green-600 text-white font-bold px-4 py-2 rounded"
                >
                  🟢 Simple Click Test
                </button>
              </div>
            </div>
          </div>
          
          {currentList.length > 0 && (
            <Button
              variant="outline"
              onClick={handleClearWishlist}
              className="text-red-600 border-red-600 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Limpiar Lista
            </Button>
          )}
        </div>

        {/* Wishlist Items */}
        {currentList.length === 0 ? (
          <div className="text-center py-16">
            <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-pure-white mb-2">
              No hay productos en tu lista de deseos
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Explora nuestra colección y guarda tus favoritos
            </p>
            <Link href="/shop">
              <Button className="bg-lime-green-dark hover:bg-lime-green text-pure-black font-medium">
                Ir a la Tienda
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence>
              {currentList.map((productId) => {
                const product = getProductDetails(productId)
                
                return (
                  <motion.div
                    key={productId}
                    layout
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="group hover:shadow-lg transition-all duration-300 bg-white dark:bg-mist-gray border-gray-200 dark:border-gray-700">
                      <CardContent className="p-4">
                        {/* Product Image */}
                        <div className="relative aspect-square mb-4 overflow-hidden rounded-lg bg-gray-100">
                          <Image
                            src={product.image}
                            alt={product.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          
                          {/* Quick Actions */}
                          <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRemoveItem(productId)}
                              className="h-8 w-8 bg-white/90 hover:bg-white text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleShare(product)}
                              className="h-8 w-8 bg-white/90 hover:bg-white text-gray-600 hover:text-gray-700"
                            >
                              <Share2 className="w-4 h-4" />
                            </Button>
                          </div>

                          {/* Stock Badge */}
                          {!product.inStock && (
                            <Badge className="absolute bottom-2 left-2 bg-red-600 text-white">
                              Agotado
                            </Badge>
                          )}
                        </div>

                        {/* Product Info */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary" className="text-xs">
                              {product.brand}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {product.category}
                            </Badge>
                          </div>
                          
                          <h3 className="font-semibold text-gray-900 dark:text-pure-white line-clamp-2">
                            {product.name}
                          </h3>
                          
                          <div className="flex items-center gap-2">
                            <span className="text-lg font-bold text-lime-green-dark">
                              ${product.price.toLocaleString()}
                            </span>
                            {product.originalPrice && (
                              <span className="text-sm text-gray-500 line-through">
                                ${product.originalPrice.toLocaleString()}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2 mt-4">
                          <Button
                            onClick={() => handleAddToCart(product)}
                            disabled={!product.inStock}
                            className="flex-1 bg-lime-green-dark hover:bg-lime-green text-pure-black font-medium disabled:opacity-50"
                          >
                            <ShoppingCart className="w-4 h-4 mr-2" />
                            {product.inStock ? 'Agregar al Carrito' : 'Agotado'}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>
        )}

        {/* Continue Shopping */}
        {currentList.length > 0 && (
          <div className="text-center mt-12">
            <Link href="/shop">
              <Button variant="outline" className="border-lime-green-dark text-lime-green-dark hover:bg-lime-green-dark hover:text-pure-black">
                Continuar Comprando
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
