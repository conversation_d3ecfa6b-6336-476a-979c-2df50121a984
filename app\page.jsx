'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

export default function HomePage() {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-warm-camel">Cargando TWL...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Glassmorphic Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-forest-emerald/10"></div>
        <div className="absolute inset-0 backdrop-blur-3xl"></div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-forest-emerald/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Logo/Brand */}
            <div className="mb-8">
              <h1 className="text-6xl md:text-8xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                <span className="text-forest-emerald dark:text-light-cloud-gray">THE</span>
                <span className="text-primary ml-4">WHITE</span>
                <span className="text-forest-emerald dark:text-light-cloud-gray ml-4">LACES</span>
              </h1>
              <p className="text-xl md:text-2xl text-warm-camel font-light">
                Luxury Streetwear Footwear
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Link href="/shop">
                <button className="bg-primary hover:bg-primary/90 text-forest-emerald px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105">
                  Explorar Colección
                </button>
              </Link>
              <Link href="/limited-editions">
                <button className="glass border border-white/20 text-forest-emerald dark:text-light-cloud-gray hover:bg-white/10 px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105">
                  Ver Drops Exclusivos
                </button>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 text-center">
              <div className="glass p-6 rounded-2xl border border-white/10">
                <div className="text-3xl font-bold text-primary">500+</div>
                <div className="text-warm-camel">Productos Premium</div>
              </div>
              <div className="glass p-6 rounded-2xl border border-white/10">
                <div className="text-3xl font-bold text-primary">50+</div>
                <div className="text-warm-camel">Marcas Exclusivas</div>
              </div>
              <div className="glass p-6 rounded-2xl border border-white/10">
                <div className="text-3xl font-bold text-primary">24h</div>
                <div className="text-warm-camel">Envío Express</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="animate-bounce">
            <svg className="w-6 h-6 text-warm-camel" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </section>

      {/* Shop The Look Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/5 dark:bg-black/10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Shop The Look
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Inspiración directa del streetwear urbano. Descubre cómo combinar nuestros productos.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Lifestyle Image 1 */}
            <div className="relative h-96 glass rounded-2xl overflow-hidden border border-white/10 group">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="text-sm font-medium">Street Style</p>
                <p className="text-xs opacity-80">Urban Essentials</p>
              </div>
              <div className="absolute inset-0 bg-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Lifestyle Image 2 */}
            <div className="relative h-96 glass rounded-2xl overflow-hidden border border-white/10 group">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="text-sm font-medium">Luxury Casual</p>
                <p className="text-xs opacity-80">Premium Comfort</p>
              </div>
              <div className="absolute inset-0 bg-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Product Card 1 */}
            <div className="glass rounded-2xl border border-white/10 overflow-hidden group hover:scale-105 transition-transform duration-300">
              <div className="h-64 bg-gradient-to-br from-primary/20 to-forest-emerald/20"></div>
              <div className="p-4">
                <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">Nike Air Force Premium</h3>
                <p className="text-sm text-warm-camel mb-3">Edición limitada streetwear</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-primary">$3,570 MXN</span>
                  <button className="bg-primary hover:bg-primary/90 text-forest-emerald px-3 py-1 rounded-lg text-sm transition-colors">
                    Ver
                  </button>
                </div>
              </div>
            </div>

            {/* Product Card 2 */}
            <div className="glass rounded-2xl border border-white/10 overflow-hidden group hover:scale-105 transition-transform duration-300">
              <div className="h-64 bg-gradient-to-br from-forest-emerald/20 to-primary/20"></div>
              <div className="p-4">
                <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">Gucci Sneakers Luxury</h3>
                <p className="text-sm text-warm-camel mb-3">Colaboración exclusiva</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-primary">$8,950 MXN</span>
                  <button className="bg-primary hover:bg-primary/90 text-forest-emerald px-3 py-1 rounded-lg text-sm transition-colors">
                    Ver
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Nuestra Colección Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Nuestra Colección
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Explora nuestras categorías premium de calzado de lujo y streetwear exclusivo.
            </p>
          </div>

          {/* Category Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {['Sneakers', 'Casual', 'Tacones', 'Sandalias', 'Formal'].map((category) => (
              <button
                key={category}
                className="glass px-6 py-3 border border-white/20 text-forest-emerald dark:text-light-cloud-gray rounded-xl hover:bg-primary hover:text-forest-emerald transition-all duration-300 transform hover:scale-105"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
              <div key={item} className="glass rounded-2xl border border-white/10 overflow-hidden group hover:scale-105 transition-transform duration-300">
                <div className="h-48 bg-gradient-to-br from-primary/20 to-forest-emerald/20"></div>
                <div className="p-4">
                  <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">Producto {item}</h3>
                  <p className="text-sm text-warm-camel mb-3">Descripción premium</p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-primary">$3,570 MXN</span>
                    <button className="bg-primary hover:bg-primary/90 text-forest-emerald px-3 py-1 rounded-lg text-sm transition-colors">
                      Ver
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Ver toda la colección button */}
          <div className="text-center">
            <Link href="/shop">
              <button className="glass border border-white/20 text-forest-emerald dark:text-light-cloud-gray hover:bg-primary hover:text-forest-emerald px-8 py-3 rounded-xl transition-all duration-300 transform hover:scale-105">
                Ver toda la colección
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Hottest Drops Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-forest-emerald/10 dark:bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Hottest Drops
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Los lanzamientos más esperados y exclusivos. Ediciones limitadas que no puedes perderte.
            </p>
          </div>

          {/* Automatic Slider */}
          <div className="flex justify-center">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 max-w-6xl">
              {[1, 2, 3, 4, 5].map((item) => (
                <div key={item} className="glass rounded-2xl border border-white/10 overflow-hidden group hover:scale-105 transition-transform duration-300">
                  <div className="h-48 bg-gradient-to-br from-primary/30 to-forest-emerald/30 relative">
                    <div className="absolute top-2 left-2">
                      <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded">
                        LIMITADO
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">Drop {item}</h3>
                    <p className="text-sm text-warm-camel mb-3">Edición exclusiva</p>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-primary">$4,570 MXN</span>
                      <button className="bg-primary hover:bg-primary/90 text-forest-emerald px-3 py-1 rounded-lg text-sm transition-colors">
                        Ver
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Mantente al Día
          </h2>
          <p className="text-warm-camel text-lg mb-8 max-w-2xl mx-auto">
            Sé el primero en conocer sobre drops exclusivos, colaboraciones limitadas y acceso temprano a nuestras colecciones.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-3 rounded-xl border border-white/20 glass backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary text-forest-emerald dark:text-light-cloud-gray placeholder-warm-camel"
            />
            <button className="bg-primary hover:bg-primary/90 text-forest-emerald font-medium px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 whitespace-nowrap">
              Suscribirse
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}

      {/* Featured Drops Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Drops Exclusivos
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Los lanzamientos más esperados y ediciones limitadas que no puedes perderte.
            </p>
          </motion.div>
          <FeaturedDrops />
        </div>
      </section>

      {/* Editorial Picks Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/5 dark:bg-black/10">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Selección Editorial
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Curada por nuestros expertos en streetwear y cultura sneaker.
            </p>
          </motion.div>
          <EditorialPicks />
        </div>
      </section>

      {/* New Arrivals Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Nuevos Arribos
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Las últimas incorporaciones a nuestra colección premium.
            </p>
          </motion.div>
          <NewArrivals />
        </div>
      </section>

      {/* Community Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/5 dark:bg-black/10">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Comunidad TWL
            </h2>
            <p className="text-warm-camel text-lg max-w-2xl mx-auto">
              Descubre cómo otros sneakerheads combinan sus TWL. Únete a la conversación.
            </p>
          </motion.div>
          <UserGeneratedContent featured={true} maxPosts={6} showCreatePost={false} />
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Mantente al Día
            </h2>
            <p className="text-warm-camel text-lg mb-8 max-w-2xl mx-auto">
              Sé el primero en conocer sobre drops exclusivos, colaboraciones limitadas y acceso temprano a nuestras colecciones.
            </p>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"
            >
              <input
                type="email"
                placeholder="<EMAIL>"
                className="flex-1 px-4 py-3 rounded-xl border border-warm-camel/20 bg-white/10 dark:bg-black/20 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary text-forest-emerald dark:text-light-cloud-gray placeholder-warm-camel"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-primary hover:bg-primary/90 text-forest-emerald font-medium px-6 py-3 rounded-xl transition-colors whitespace-nowrap"
              >
                Suscribirse
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

      {/* Shop The Look Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Shop The Look
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Inspiración directa del streetwear urbano. Descubre cómo combinar nuestros productos.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Lifestyle Image 1 */}
            <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="text-sm font-medium">Street Style</p>
                <p className="text-xs opacity-80">Urban Essentials</p>
              </div>
            </div>

            {/* Lifestyle Image 2 */}
            <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="text-sm font-medium">Luxury Casual</p>
                <p className="text-xs opacity-80">Premium Comfort</p>
              </div>
            </div>

            {/* Product Card 1 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-64 bg-gray-200"></div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Nike Air Force Premium</h3>
                <p className="text-sm text-gray-600 mb-3">Edición limitada streetwear</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-lime-600">$3,570 MXN</span>
                  <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                    Ver
                  </button>
                </div>
              </div>
            </div>

            {/* Product Card 2 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-64 bg-gray-200"></div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Gucci Sneakers Luxury</h3>
                <p className="text-sm text-gray-600 mb-3">Colaboración exclusiva</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-lime-600">$8,950 MXN</span>
                  <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                    Ver
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Nuestra Colección Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Nuestra Colección
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explora nuestras categorías premium de calzado de lujo y streetwear exclusivo.
            </p>
          </div>

          {/* Category Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {['Sneakers', 'Casual', 'Tacones', 'Sandalias', 'Formal'].map((category) => (
              <button
                key={category}
                className="px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 rounded-lg hover:border-lime-500 hover:text-lime-600 transition-colors"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
              <div key={item} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Producto {item}</h3>
                  <p className="text-sm text-gray-600 mb-3">Descripción premium</p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-lime-600">$3,570 MXN</span>
                    <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                      Ver
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Ver toda la colección button */}
          <div className="text-center">
            <button className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-lg transition-colors">
              Ver toda la colección
            </button>
          </div>
        </div>
      </section>

      {/* Hottest Drops Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Hottest Drops
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Los lanzamientos más esperados y exclusivos. Ediciones limitadas que no puedes perderte.
            </p>
          </div>

          {/* Automatic Slider */}
          <div className="flex justify-center">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 max-w-6xl">
              {[1, 2, 3, 4, 5].map((item) => (
                <div key={item} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <div className="h-48 bg-gray-200 relative">
                    <div className="absolute top-2 left-2">
                      <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded">
                        LIMITADO
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2">Drop {item}</h3>
                    <p className="text-sm text-gray-600 mb-3">Edición exclusiva</p>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-lime-600">$4,570 MXN</span>
                      <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                        Ver
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Mantente al Día
          </h2>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Sé el primero en conocer sobre drops exclusivos, colaboraciones limitadas y acceso temprano a nuestras colecciones.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-lime-500"
            />
            <button className="bg-lime-500 hover:bg-lime-600 text-black font-medium px-6 py-3 rounded-lg transition-colors whitespace-nowrap">
              Suscribirse
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}
